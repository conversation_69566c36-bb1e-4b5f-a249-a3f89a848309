#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to scrape and display all players from the Israeli Winner League
using Selenium WebDriver to impersonate Chrome browser.
URL: https://www.proballers.com/basketball/league/172/israel-winner-league/players
"""

import time
import sys
import re
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup


def setup_chrome_driver():
    """
    Set up Chrome WebDriver with options to mimic a real browser
    """
    chrome_options = Options()
    
    # Add arguments to make the browser look more like a real user
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # Set user agent to look like a real Chrome browser
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    # Optional: Run in headless mode (uncomment if you don't want to see the browser)
    # chrome_options.add_argument("--headless")
    
    # Set up the service with ChromeDriverManager
    service = Service(ChromeDriverManager().install())
    
    # Create the driver
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    # Execute script to remove webdriver property
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver


def scrape_players_with_selenium():
    """
    Scrape player data using Selenium WebDriver
    """
    url = "https://www.proballers.com/basketball/league/172/israel-winner-league/players"
    
    print("Setting up Chrome WebDriver...")
    driver = setup_chrome_driver()
    
    try:
        print(f"Navigating to: {url}")
        driver.get(url)
        
        # Wait for the page to load
        print("Waiting for page to load...")
        wait = WebDriverWait(driver, 20)
        
        # Try different approaches to wait for content
        try:
            # First try to wait for any links containing 'basketball/player'
            wait.until(EC.presence_of_element_located((By.XPATH, "//a[contains(@href, '/basketball/player/')]")))
            print("Found player links using XPath")
        except TimeoutException:
            print("Timeout waiting for player links with XPath, trying alternative approach...")
            try:
                # Wait for the main content to load
                wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                print("Page body loaded, continuing...")
            except TimeoutException:
                print("Page failed to load completely, but continuing with current content...")

        # Give extra time for dynamic content to load
        time.sleep(5)

        print("Extracting player data from page source...")

        # Get the page source and parse with BeautifulSoup
        page_source = driver.page_source

        # Debug: Save the page source to see what we're getting
        with open('debug_page_source.html', 'w', encoding='utf-8') as f:
            f.write(page_source)
        print("Page source saved to debug_page_source.html for inspection")

        soup = BeautifulSoup(page_source, 'html.parser')
        
        # Find all player links
        player_links = soup.find_all('a', href=lambda x: x and '/basketball/player/' in x)

        print(f"Found {len(player_links)} player links. Processing...")

        # If no player links found, try alternative parsing
        if len(player_links) == 0:
            print("No player links found with standard method, trying alternative parsing...")
            # Look for any links that might be players
            all_links = soup.find_all('a', href=True)
            player_links = [link for link in all_links if 'player' in link.get('href', '').lower()]
            print(f"Found {len(player_links)} potential player links with alternative method")

            # If still no links, let's see what we have in the page
            if len(player_links) == 0:
                print("Still no player links found. Checking page content...")
                # Look for any text that might indicate players
                text_content = soup.get_text()
                if 'player' in text_content.lower():
                    print("Page contains 'player' text, but no structured links found")
                else:
                    print("Page doesn't seem to contain player information")
                    print("First 500 characters of page:")
                    print(text_content[:500])

        players = []
        processed_names = set()
        
        for link in player_links:
            player_name = link.get_text(strip=True)
            
            # Skip empty or invalid names
            if not player_name or len(player_name) <= 1 or player_name in processed_names:
                continue
            
            # Clean up the player name
            player_name = ' '.join(player_name.split())
            processed_names.add(player_name)
            
            # Initialize player data
            player_data = {
                'name': player_name,
                'team': '',
                'age': '',
                'height': '',
                'country': ''
            }
            
            # Try to find the container with all player information
            # Look for the parent elements that might contain the full player info
            container = link.find_parent()
            attempts = 0
            
            while container and attempts < 8:
                container_text = container.get_text(separator=' ', strip=True)
                
                # Look for team links in this container
                team_links = container.find_all('a', href=lambda x: x and '/basketball/team/' in x)
                if team_links and not player_data['team']:
                    teams = []
                    for team_link in team_links:
                        team_name = team_link.get_text(strip=True)
                        if team_name and team_name not in teams:
                            teams.append(team_name)
                    if teams:
                        player_data['team'] = ', '.join(teams)
                
                # Extract age, height, and country from the text
                text_parts = container_text.split()
                
                # Look for age (reasonable basketball player age)
                if not player_data['age']:
                    for part in text_parts:
                        if part.isdigit() and 15 <= int(part) <= 45:
                            player_data['age'] = part
                            break
                
                # Look for height (contains 'm' and digits)
                if not player_data['height']:
                    for part in text_parts:
                        if 'm' in part and any(c.isdigit() for c in part) and len(part) <= 6:
                            player_data['height'] = part
                            break
                
                # Look for country names
                if not player_data['country']:
                    countries = [
                        'Israel', 'United States', 'France', 'Canada', 'Lithuania', 'Brazil',
                        'Ghana', 'Angola', 'Netherlands', 'Belgium', 'Cuba', 'Panama',
                        'Nigeria', 'Denmark', 'Argentina', 'Spain', 'Germany', 'Italy',
                        'Greece', 'Serbia', 'Croatia', 'Slovenia', 'Turkey', 'Russia',
                        'Australia', 'New Zealand', 'Japan', 'South Korea', 'China'
                    ]
                    
                    for country in countries:
                        if country in container_text:
                            player_data['country'] = country
                            break
                
                # If we found team info, we're probably in the right container
                if player_data['team']:
                    break
                    
                container = container.find_parent()
                attempts += 1
            
            players.append(player_data)
        
        return players
        
    except TimeoutException:
        print("Timeout waiting for page to load")
        return []
    except Exception as e:
        print(f"Error during scraping: {e}")
        return []
    finally:
        print("Closing browser...")
        driver.quit()


def main():
    """Main function"""
    print("Israeli Winner League Players Scraper (Selenium)")
    print("=" * 55)
    
    players = scrape_players_with_selenium()
    
    if not players:
        print("No players found or error occurred during scraping.")
        sys.exit(1)
    
    # Sort players alphabetically by name
    players.sort(key=lambda x: x['name'])
    
    # Print results
    print(f"\nFound {len(players)} players in the Israeli Winner League:")
    print("-" * 55)
    
    for i, player in enumerate(players, 1):
        print(f"{i:3d}. {player['name']}")
        if player['team']:
            print(f"     Team: {player['team']}")
        if player['age']:
            print(f"     Age: {player['age']}")
        if player['height']:
            print(f"     Height: {player['height']}")
        if player['country']:
            print(f"     Country: {player['country']}")
        print()
    
    print("=" * 55)
    print(f"Total players: {len(players)}")
    
    # Optionally save to file
    save_to_file = input("\nWould you like to save the results to a file? (y/n): ").lower().strip()
    if save_to_file == 'y':
        filename = f"israel_winner_league_players_selenium_{time.strftime('%Y%m%d_%H%M%S')}.txt"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("Israeli Winner League Players (2024-2025 season) - Scraped with Selenium\n")
                f.write("=" * 70 + "\n\n")
                
                for i, player in enumerate(players, 1):
                    f.write(f"{i:3d}. {player['name']}\n")
                    if player['team']:
                        f.write(f"     Team: {player['team']}\n")
                    if player['age']:
                        f.write(f"     Age: {player['age']}\n")
                    if player['height']:
                        f.write(f"     Height: {player['height']}\n")
                    if player['country']:
                        f.write(f"     Country: {player['country']}\n")
                    f.write("\n")
                
                f.write("=" * 70 + "\n")
                f.write(f"Total players: {len(players)}\n")
            
            print(f"Results saved to: {filename}")
        except Exception as e:
            print(f"Error saving to file: {e}")


if __name__ == "__main__":
    main()
