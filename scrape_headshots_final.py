#!/usr/bin/env python3
"""
Final script to scrape player headshots using correct URLs with IDs
"""

import time
import sys
import csv
import os
import re
import requests
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
from urllib.parse import urljoin


def setup_chrome_driver():
    """Set up Chrome WebDriver"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    chrome_options.add_argument("--headless")  # Run headless for speed
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.set_page_load_timeout(15)
    driver.implicitly_wait(3)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver


def extract_player_urls_from_html():
    """Extract player URLs with IDs from the saved HTML"""
    html_file = "debug_page_source.html"
    
    if not os.path.exists(html_file):
        print(f"HTML file {html_file} not found")
        return []
    
    print(f"Extracting player URLs from {html_file}")
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    soup = BeautifulSoup(content, 'html.parser')
    
    # Find all player links
    player_links = soup.find_all('a', href=lambda x: x and '/basketball/player/' in x)
    
    players = []
    for link in player_links:
        href = link.get('href')
        title = link.get('title', '')
        text = link.get_text(strip=True)
        
        if href and title:
            # Clean up the player name
            player_name = title.strip()
            if player_name:
                full_url = urljoin('https://www.proballers.com', href)
                players.append({
                    'name': player_name,
                    'url': full_url
                })
    
    print(f"Found {len(players)} players with URLs")
    return players


def extract_headshot_from_page(driver, player_url):
    """Extract headshot from a player's page"""
    try:
        print(f"    Loading: {player_url}")
        driver.get(player_url)
        
        # Wait a bit for page to load
        time.sleep(2)
        
        # Get page source
        page_source = driver.page_source
        soup = BeautifulSoup(page_source, 'html.parser')
        
        # Look for player images with various strategies
        image_selectors = [
            'img[src*="player"]',
            'img[src*="headshot"]',
            'img[src*="photo"]',
            'img[alt*="player"]',
            'img[alt*="Player"]',
            '.player-photo img',
            '.player-image img',
            '.headshot img'
        ]
        
        for selector in image_selectors:
            images = soup.select(selector)
            for img in images:
                src = img.get('src')
                if src:
                    if src.startswith('/'):
                        src = urljoin('https://www.proballers.com', src)
                    
                    # Check if it looks like a player photo
                    if any(keyword in src.lower() for keyword in ['player', 'headshot', 'photo', 'portrait']):
                        print(f"    Found headshot: {src}")
                        return src
        
        # If no specific player image found, look for any reasonable image
        all_images = soup.find_all('img', src=True)
        for img in all_images:
            src = img.get('src')
            alt = img.get('alt', '').lower()
            
            # Skip obvious non-player images
            if any(skip in src.lower() for skip in ['logo', 'icon', 'flag', 'banner', 'ad', 'sponsor']):
                continue
            if any(skip in alt for skip in ['logo', 'icon', 'flag', 'banner', 'ad', 'sponsor']):
                continue
            
            # Make sure it's a full URL
            if src.startswith('/'):
                src = urljoin('https://www.proballers.com', src)
            
            # Check if it's a reasonable image URL
            if any(ext in src.lower() for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']):
                print(f"    Found image: {src}")
                return src
        
        print(f"    No headshot found")
        return "NON_FOUND"
        
    except Exception as e:
        print(f"    Error: {e}")
        return "NON_FOUND"


def main():
    """Main function"""
    print("Israeli Winner League Player Headshots Scraper (Final)")
    print("=" * 60)
    
    # Extract player URLs from HTML
    players = extract_player_urls_from_html()
    
    if not players:
        print("No players found. Exiting.")
        return
    
    # CSV setup
    csv_filename = "headshots.csv"
    
    if os.path.exists(csv_filename):
        choice = input(f"{csv_filename} exists. (a)ppend, (o)verwrite, or (c)ancel? ").lower().strip()
        if choice == 'c':
            return
        mode = 'w' if choice == 'o' else 'a'
    else:
        mode = 'w'
    
    # Setup driver
    print("Setting up Chrome WebDriver...")
    driver = setup_chrome_driver()
    
    try:
        with open(csv_filename, mode, newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            if mode == 'w':
                writer.writerow(['player_name', 'headshot_url'])
            
            successful_extractions = 0
            
            for i, player in enumerate(players, 1):
                print(f"\n[{i}/{len(players)}] Processing: {player['name']}")
                
                try:
                    headshot_url = extract_headshot_from_page(driver, player['url'])
                    
                    if headshot_url != "NON_FOUND":
                        successful_extractions += 1
                    
                    # Write to CSV
                    writer.writerow([player['name'], headshot_url])
                    csvfile.flush()
                    
                    print(f"    ✅ Result: {headshot_url}")
                    
                except Exception as e:
                    print(f"    ❌ Error: {e}")
                    writer.writerow([player['name'], "NON_FOUND"])
                    csvfile.flush()
                
                # Progress update
                if i % 10 == 0:
                    success_rate = (successful_extractions / i) * 100
                    print(f"\n📊 Progress: {i}/{len(players)} ({i/len(players)*100:.1f}%)")
                    print(f"📈 Success rate: {successful_extractions}/{i} ({success_rate:.1f}%)")
                
                # Small delay to be respectful
                time.sleep(0.5)
        
        print(f"\n✅ Completed! Results saved to {csv_filename}")
        print(f"📊 Final stats: {successful_extractions}/{len(players)} headshots found ({successful_extractions/len(players)*100:.1f}%)")
        
    except KeyboardInterrupt:
        print("\n⚠️  Interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        print("Closing browser...")
        driver.quit()


if __name__ == "__main__":
    main()
