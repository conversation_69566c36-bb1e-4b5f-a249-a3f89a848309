#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to download list of player URLs from Israeli Winner League
Saves player names and their homepage URLs to a CSV file
"""

import time
import csv
import sys
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
from urllib.parse import urljoin


def setup_chrome_driver():
    """
    Set up Chrome WebDriver with options to mimic a real browser
    """
    chrome_options = Options()
    
    # Add arguments to make the browser look more like a real user
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # Set user agent to look like a real Chrome browser
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    # Optional: Run in headless mode for faster processing
    chrome_options.add_argument("--headless")
    
    # Set up the service with ChromeDriverManager
    service = Service(ChromeDriverManager().install())
    
    # Create the driver
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    # Set timeouts
    driver.set_page_load_timeout(30)
    driver.implicitly_wait(5)
    
    # Execute script to remove webdriver property
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver


def get_player_urls():
    """
    Get all player URLs from the main Israeli Winner League players page
    """
    url = "https://www.proballers.com/basketball/league/172/israel-winner-league/players"
    
    print(f"Fetching player URLs from: {url}")
    driver = setup_chrome_driver()
    
    try:
        print("Loading page...")
        driver.get(url)
        
        # Wait for the page to load
        wait = WebDriverWait(driver, 20)
        
        try:
            # Wait for player links to be present
            wait.until(EC.presence_of_element_located((By.XPATH, "//a[contains(@href, '/basketball/player/')]")))
            print("Player links found")
        except TimeoutException:
            print("Timeout waiting for player links")
            return []
        
        # Give extra time for dynamic content to load
        time.sleep(3)
        
        print("Extracting player data...")
        
        # Get the page source and parse with BeautifulSoup
        page_source = driver.page_source
        soup = BeautifulSoup(page_source, 'html.parser')
        
        # Find all player links
        player_links = soup.find_all('a', href=lambda x: x and '/basketball/player/' in x)
        
        print(f"Found {len(player_links)} player links")
        
        players = []
        processed_names = set()
        
        for link in player_links:
            href = link.get('href')
            title = link.get('title', '')
            text = link.get_text(strip=True)
            
            # Use title if available, otherwise use text
            player_name = title.strip() if title else text.strip()
            
            # Skip empty or invalid names
            if not player_name or len(player_name) <= 1 or player_name in processed_names:
                continue
            
            processed_names.add(player_name)
            
            if href:
                # Make sure it's a full URL
                if href.startswith('/'):
                    full_url = urljoin('https://www.proballers.com', href)
                else:
                    full_url = href
                
                players.append({
                    'name': player_name,
                    'url': full_url
                })
        
        print(f"Extracted {len(players)} unique players")
        return players
        
    except Exception as e:
        print(f"Error: {e}")
        return []
    finally:
        print("Closing browser...")
        driver.quit()


def save_player_urls(players, filename="player_urls.csv"):
    """
    Save player URLs to CSV file
    """
    print(f"Saving {len(players)} players to {filename}")
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header
        writer.writerow(['player_name', 'player_url'])
        
        # Write player data
        for player in players:
            writer.writerow([player['name'], player['url']])
    
    print(f"✅ Player URLs saved to {filename}")


def main():
    """Main function"""
    print("Israeli Winner League - Player URL Extractor")
    print("=" * 50)
    
    # Get player URLs
    players = get_player_urls()
    
    if not players:
        print("❌ No players found. Exiting.")
        sys.exit(1)
    
    # Save to CSV
    save_player_urls(players)
    
    print(f"\n📊 Summary:")
    print(f"   Total players: {len(players)}")
    print(f"   Output file: player_urls.csv")
    print(f"\nNext step: Run 'python3 scrape_headshots.py' to extract headshots")


if __name__ == "__main__":
    main()
