#!/usr/bin/env python3
"""
Basketball Player Headshot Finder

This script reads a text file containing basketball player names and creates
a CSV file with search URLs for finding their headshot images on Pro Ballers.
Due to anti-bot protection, manual lookup is required.

Usage: python headshots.py <input_file>
"""

import sys
import csv
import time
from urllib.parse import quote_plus


def create_search_url(player_name):
    """
    Create a Pro Ballers search URL for the given player name.

    Args:
        player_name (str): The name of the basketball player

    Returns:
        str: The search URL for manual lookup
    """
    # Create search URL for manual lookup
    name_parts = player_name.strip().split()
    if len(name_parts) >= 2:
        # Try Last First format (common on Pro Ballers)
        search_term = f"{name_parts[1]} {name_parts[0]}"
    else:
        search_term = player_name.strip()

    search_url = (f"https://www.proballers.com/search?q="
                  f"{quote_plus(search_term)}")
    return search_url


def search_proballers_api(player_name):
    """
    Attempt to use the Pro Ballers search_player API to find player URLs.

    Args:
        player_name (str): The name of the basketball player

    Returns:
        str: Player URL if found, search URL for manual lookup otherwise
    """
    try:
        import requests
        import json

        # Try the search_player API endpoint
        api_url = "https://www.proballers.com/search_player"

        # Headers to mimic a real browser request
        headers = {
            'User-Agent': ('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) '
                           'AppleWebKit/537.36 (KHTML, like Gecko) '
                           'Chrome/120.0.0.0 Safari/537.36'),
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Content-Type': 'application/json',
            'Origin': 'https://www.proballers.com',
            'Referer': 'https://www.proballers.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        }

        # Try different payload formats
        payloads = [
            {"query": player_name},
            {"q": player_name},
            {"search": player_name},
            {"name": player_name}
        ]

        session = requests.Session()
        session.headers.update(headers)

        for payload in payloads:
            try:
                print(f"    Trying API with payload: {payload}")

                # Try POST request
                response = session.post(api_url, json=payload, timeout=10)

                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"    API Response: {data}")

                        # Look for player URLs in the response
                        if isinstance(data, list) and len(data) > 0:
                            for item in data:
                                if isinstance(item, dict) and 'url' in item:
                                    player_url = item['url']
                                    if '/basketball/player/' in player_url:
                                        return get_headshot_from_player_url(
                                            player_url, player_name)

                        elif isinstance(data, dict):
                            if 'players' in data and len(data['players']) > 0:
                                player_url = data['players'][0].get('url')
                                if player_url and '/basketball/player/' in player_url:
                                    return get_headshot_from_player_url(
                                        player_url, player_name)

                    except json.JSONDecodeError:
                        print(f"    Non-JSON response: {response.text[:200]}")

                elif response.status_code == 403:
                    print("    API blocked by Cloudflare protection")
                    break

                else:
                    print(f"    API returned status {response.status_code}")

                time.sleep(1)  # Respectful delay between attempts

            except Exception as e:
                print(f"    API attempt failed: {str(e)}")
                continue

        # If API fails, fall back to manual search URL
        print("    API approach failed, falling back to manual search")
        return create_search_url(player_name)

    except ImportError:
        print("    Requests library not available, using manual search")
        return create_search_url(player_name)
    except Exception as e:
        print(f"    Error with API approach: {str(e)}")
        return create_search_url(player_name)


def get_headshot_from_player_url(player_url, player_name):
    """
    Extract headshot URL from a Pro Ballers player page URL.

    Args:
        player_url (str): URL of the player's Pro Ballers page
        player_name (str): Name of the player for verification

    Returns:
        str: The headshot URL if found, player URL otherwise
    """
    try:
        import requests
        from bs4 import BeautifulSoup

        headers = {
            'User-Agent': ('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) '
                          'AppleWebKit/537.36 (KHTML, like Gecko) '
                          'Chrome/120.0.0.0 Safari/537.36')
        }

        # Ensure full URL
        if not player_url.startswith('http'):
            player_url = 'https://www.proballers.com' + player_url

        print(f"    Fetching player page: {player_url}")

        response = requests.get(player_url, headers=headers, timeout=10)

        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')

            # Look for the main player image
            img_tags = soup.find_all('img')

            for img in img_tags:
                src = img.get('src', '')
                alt = img.get('alt', '')

                # Check if this looks like a player headshot
                if src and 'proballers.com' in src:
                    # Look for player-related image paths
                    if any(keyword in src.lower() for keyword in
                          ['player', 'ul/player', 'media/cache']):
                        # Verify it's not a logo or other image
                        if not any(keyword in src.lower() for keyword in
                                  ['logo', 'flag', 'country']):
                            # Clean up the URL
                            if src.startswith('//'):
                                src = 'https:' + src
                            elif src.startswith('/'):
                                src = 'https://www.proballers.com' + src

                            print(f"    Found headshot: {src}")
                            return src

            print("    No headshot found on player page")
            return player_url  # Return player URL for manual inspection

        else:
            print(f"    Failed to fetch player page: {response.status_code}")
            return player_url

    except Exception as e:
        print(f"    Error fetching headshot: {str(e)}")
        return player_url


def search_proballers(player_name):
    """
    Search for a player on Pro Ballers website and return their headshot URL.

    Args:
        player_name (str): The name of the basketball player

    Returns:
        str: Headshot URL, player URL, or search URL for manual lookup
    """
    print(f"  Attempting API search for: {player_name}")

    # Try the API approach first
    result = search_proballers_api(player_name)

    # If we got a search URL back, it means API failed
    if 'proballers.com/search' in result:
        print(f"  Manual search required: {result}")

    return result

def create_html_helper(results, html_filename):
    """Create an HTML helper file using the search_player API."""
    player_names_js = str([name for name, _ in results]).replace("'", '"')

    html_content = f"""<!DOCTYPE html>
<html>
<head>
    <title>Pro Ballers Player Lookup Helper</title>
    <style>
        body {{ font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }}
        .player-card {{ border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }}
        .player-name {{ font-size: 18px; font-weight: bold; color: #2c3e50; }}
        .search-btn {{ background: #3498db; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px 0; }}
        .search-btn:hover {{ background: #2980b9; }}
        .results {{ margin-top: 10px; }}
        .player-result {{ border: 1px solid #27ae60; padding: 10px; margin: 5px 0; border-radius: 3px; cursor: pointer; }}
        .player-result:hover {{ background: #ecf0f1; }}
        .input-field {{ width: 100%; padding: 5px; margin: 5px 0; }}
        .download-btn {{ background: #27ae60; color: white; padding: 15px 30px; border: none; border-radius: 4px; font-size: 16px; cursor: pointer; margin: 20px 0; }}
        .status {{ font-weight: bold; }}
        .found {{ color: #27ae60; }}
        .not-found {{ color: #e74c3c; }}
        .loading {{ color: #f39c12; }}
    </style>
</head>
<body>
    <h1>Pro Ballers Player Headshot Lookup</h1>
    <p>This tool uses the Pro Ballers search_player API to find players and their headshots.</p>

    <div id="players-container"></div>

    <button class="download-btn" onclick="downloadCSV()">Download Updated CSV</button>

    <script>
        const players = {player_names_js};
        let playerData = [];

        // Initialize player data
        players.forEach((name, index) => {{
            playerData.push({{
                name: name,
                playerUrl: '',
                headshotUrl: '',
                status: 'PENDING'
            }});
        }});

        function createPlayerCard(playerName, index) {{
            return `
                <div class="player-card">
                    <div class="player-name">${{playerName}}</div>
                    <button class="search-btn" onclick="searchPlayer('${{playerName}}', ${{index}})">
                        Search Player
                    </button>
                    <div id="status-${{index}}" class="status">Ready to search</div>
                    <div id="results-${{index}}" class="results"></div>
                    <div style="margin-top: 10px;">
                        <input type="text" id="player-url-${{index}}" class="input-field"
                               placeholder="Player URL (will be filled automatically)" readonly>
                        <input type="text" id="headshot-url-${{index}}" class="input-field"
                               placeholder="Headshot URL (will be filled automatically)" readonly>
                    </div>
                </div>
            `;
        }}

        function initializePage() {{
            const container = document.getElementById('players-container');
            players.forEach((name, index) => {{
                container.innerHTML += createPlayerCard(name, index);
            }});
        }}

        async function searchPlayer(playerName, index) {{
            const statusDiv = document.getElementById(`status-${{index}}`);
            const resultsDiv = document.getElementById(`results-${{index}}`);
            const playerUrlInput = document.getElementById(`player-url-${{index}}`);
            const headshotUrlInput = document.getElementById(`headshot-url-${{index}}`);

            statusDiv.innerHTML = '<span class="loading">Searching...</span>';
            resultsDiv.innerHTML = '';

            try {{
                // Try the search_player API
                const response = await fetch('https://www.proballers.com/search_player', {{
                    method: 'POST',
                    headers: {{
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
                    }},
                    body: JSON.stringify({{ query: playerName }})
                }});

                if (response.ok) {{
                    const data = await response.json();

                    if (data && data.length > 0) {{
                        statusDiv.innerHTML = '<span class="found">Players found! Click on a result:</span>';

                        data.slice(0, 5).forEach((player, playerIndex) => {{
                            const playerDiv = document.createElement('div');
                            playerDiv.className = 'player-result';
                            playerDiv.innerHTML = `
                                <strong>${{player.name || 'Unknown'}}</strong><br>
                                <small>${{player.team || 'No team info'}}</small>
                            `;
                            playerDiv.onclick = () => selectPlayer(player, index);
                            resultsDiv.appendChild(playerDiv);
                        }});
                    }} else {{
                        statusDiv.innerHTML = '<span class="not-found">No players found</span>';
                        playerData[index].status = 'NOT_FOUND';
                    }}
                }} else {{
                    throw new Error(`HTTP ${{response.status}}`);
                }}

            }} catch (error) {{
                console.error('API Error:', error);
                statusDiv.innerHTML = '<span class="not-found">API blocked - Manual search required</span>';

                // Fallback to manual search URL
                const searchUrl = `https://www.proballers.com/search?q=${{encodeURIComponent(playerName)}}`;
                resultsDiv.innerHTML = `
                    <div style="margin-top: 10px;">
                        <a href="${{searchUrl}}" target="_blank" style="color: #3498db;">
                            Open manual search in new tab
                        </a>
                        <br><small>Copy player page URL and headshot URL manually</small>
                        <br>
                        <input type="text" placeholder="Paste player URL here"
                               onchange="updatePlayerUrl(${{index}}, this.value)" style="width: 100%; margin: 5px 0;">
                        <input type="text" placeholder="Paste headshot URL here"
                               onchange="updateHeadshotUrl(${{index}}, this.value)" style="width: 100%; margin: 5px 0;">
                    </div>
                `;
            }}
        }}

        async function selectPlayer(player, index) {{
            const statusDiv = document.getElementById(`status-${{index}}`);
            const playerUrlInput = document.getElementById(`player-url-${{index}}`);
            const headshotUrlInput = document.getElementById(`headshot-url-${{index}}`);

            statusDiv.innerHTML = '<span class="loading">Fetching player page...</span>';

            // Construct player URL
            const playerUrl = player.url || `https://www.proballers.com/basketball/player/${{player.id}}/${{player.slug}}`;
            playerUrlInput.value = playerUrl;
            playerData[index].playerUrl = playerUrl;

            try {{
                // Try to fetch the player page to get headshot
                const response = await fetch(playerUrl);
                const html = await response.text();

                // Look for headshot image in the HTML
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const images = doc.querySelectorAll('img');

                let headshotUrl = '';
                for (let img of images) {{
                    const src = img.src || img.getAttribute('src');
                    if (src && src.includes('proballers.com') &&
                        (src.includes('player') || src.includes('ul/player') || src.includes('media/cache'))) {{
                        if (!src.includes('logo') && !src.includes('flag') && !src.includes('country')) {{
                            headshotUrl = src;
                            break;
                        }}
                    }}
                }}

                if (headshotUrl) {{
                    headshotUrlInput.value = headshotUrl;
                    playerData[index].headshotUrl = headshotUrl;
                    playerData[index].status = 'FOUND';
                    statusDiv.innerHTML = '<span class="found">✓ Headshot found!</span>';
                }} else {{
                    playerData[index].status = 'NO_HEADSHOT';
                    statusDiv.innerHTML = '<span class="not-found">Player found but no headshot available</span>';
                }}

            }} catch (error) {{
                console.error('Error fetching player page:', error);
                playerData[index].status = 'NO_HEADSHOT';
                statusDiv.innerHTML = '<span class="not-found">Player found but could not fetch headshot</span>';
            }}
        }}

        function updatePlayerUrl(index, url) {{
            playerData[index].playerUrl = url;
            document.getElementById(`player-url-${{index}}`).value = url;
        }}

        function updateHeadshotUrl(index, url) {{
            playerData[index].headshotUrl = url;
            playerData[index].status = url ? 'FOUND' : 'PENDING';
            document.getElementById(`headshot-url-${{index}}`).value = url;
        }}

        function downloadCSV() {{
            let csv = "Player Name,Search URL,Player URL,Headshot URL,Status\\n";

            playerData.forEach(player => {{
                const searchUrl = `https://www.proballers.com/search?q=${{encodeURIComponent(player.name)}}`;
                csv += `"${{player.name}}","${{searchUrl}}","${{player.playerUrl}}","${{player.headshotUrl}}","${{player.status}}"\\n`;
            }});

            const blob = new Blob([csv], {{ type: 'text/csv' }});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'player_headshots_completed.csv';
            a.click();
            URL.revokeObjectURL(url);
        }}

        // Initialize the page
        initializePage();
    </script>
</body>
</html>"""

    with open(html_filename, 'w', encoding='utf-8') as f:
        f.write(html_content)




def process_players_file(input_filename):
    """
    Process the input file containing player names and create a CSV with headshot URLs.

    Args:
        input_filename (str): Path to the input text file containing player names
    """
    output_filename = input_filename.replace('.txt', '_headshots.csv')

    try:
        with open(input_filename, 'r', encoding='utf-8') as infile:
            player_names = [line.strip() for line in infile if line.strip()]

        print(f"Found {len(player_names)} players to process")

        results = []

        for i, player_name in enumerate(player_names, 1):
            print(f"Processing {i}/{len(player_names)}: {player_name}")

            headshot_url = search_proballers(player_name)
            results.append([player_name, headshot_url])

            print(f"  Result: {headshot_url}")

            # Add a small delay to be respectful to the server
            time.sleep(1)

        # Write results to CSV
        with open(output_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Player Name', 'Search URL', 'Player URL',
                             'Headshot URL', 'Status'])

            for player_name, search_url in results:
                writer.writerow([player_name, search_url, '', '', 'PENDING'])

        # Create an HTML helper file for easier manual lookup
        html_filename = output_filename.replace('.csv', '_helper.html')
        create_html_helper(results, html_filename)

        print(f"\nResults saved to: {output_filename}")
        print(f"HTML helper created: {html_filename}")
        print("\nEFFICIENT MANUAL LOOKUP WORKFLOW:")
        print("=" * 50)
        print("OPTION 1 - Use HTML Helper (Recommended):")
        print(f"1. Open {html_filename} in your web browser")
        print("2. Click on each player's search link")
        print("3. Copy the player page URL and headshot image URL")
        print("4. Paste them into the HTML form")
        print("5. Click 'Download Updated CSV' when done")
        print("\nOPTION 2 - Manual CSV Editing:")
        print("1. Open the CSV file in a spreadsheet application")
        print("2. For each player, visit the Search URL")
        print("3. Find the player's profile page")
        print("4. Copy the player page URL to 'Player URL' column")
        print("5. Right-click on headshot image, copy URL to 'Headshot URL'")
        print("6. Change 'Status' to 'FOUND' or 'NOT_FOUND'")
        print("\nTIP: Look for headshot images with URLs containing:")
        print("- 'proballers.com/media/cache/resize_600_png/'")
        print("- 'proballers.com/ul/player/'")

        # Print summary
        print(f"\nSummary: {len(results)} players processed")
        print("All players require manual headshot URL lookup.")

    except FileNotFoundError:
        print(f"Error: File '{input_filename}' not found")
        sys.exit(1)
    except Exception as e:
        print(f"Error processing file: {str(e)}")
        sys.exit(1)


def main():
    """Main function to handle command line arguments and run the program."""
    if len(sys.argv) != 2:
        print("Usage: python headshots.py <input_file>")
        print("Example: python headshots.py player_names_germany.txt")
        sys.exit(1)

    input_file = sys.argv[1]

    print("Basketball Player Headshot Finder")
    print("=" * 40)
    print(f"Input file: {input_file}")

    # No additional packages required for basic functionality

    process_players_file(input_file)


if __name__ == "__main__":
    main()
