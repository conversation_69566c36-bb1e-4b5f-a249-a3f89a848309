#!/usr/bin/env python3
"""
Script to scrape player headshots from their individual pages
Reads player URLs from CSV file and extracts headshot URLs
"""

import time
import csv
import sys
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
from urllib.parse import urljoin


def setup_chrome_driver():
    """Set up Chrome WebDriver with robust options"""
    chrome_options = Options()
    
    # Basic options for stability and speed
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    
    # Anti-detection
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # User agent
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    # Run headless for speed
    chrome_options.add_argument("--headless")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    # Set timeouts
    driver.set_page_load_timeout(15)
    driver.implicitly_wait(3)
    
    # Remove webdriver property
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver


def load_player_urls(filename="player_urls.csv"):
    """Load player URLs from CSV file"""
    if not os.path.exists(filename):
        print(f"❌ File {filename} not found!")
        print("Please run 'python3 get_player_urls.py' first to generate the player URLs file.")
        return []
    
    players = []
    
    with open(filename, 'r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        
        for row in reader:
            if 'player_name' in row and 'player_url' in row:
                players.append({
                    'name': row['player_name'],
                    'url': row['player_url']
                })
    
    print(f"📂 Loaded {len(players)} players from {filename}")
    return players


def extract_headshot_url(driver, player_url):
    """Extract headshot URL from a player's page"""
    try:
        print(f"    Loading: {player_url}")
        driver.get(player_url)
        
        # Wait a bit for page to load
        time.sleep(2)
        
        # Get page source
        page_source = driver.page_source
        soup = BeautifulSoup(page_source, 'html.parser')
        
        # Look for player images with various strategies
        image_selectors = [
            'img[src*="player"]',
            'img[src*="headshot"]',
            'img[src*="photo"]',
            'img[alt*="player"]',
            'img[alt*="Player"]',
            '.player-photo img',
            '.player-image img',
            '.headshot img'
        ]
        
        # Try specific selectors first
        for selector in image_selectors:
            images = soup.select(selector)
            for img in images:
                src = img.get('src')
                if src:
                    if src.startswith('/'):
                        src = urljoin('https://www.proballers.com', src)
                    
                    # Check if it looks like a player photo
                    if any(keyword in src.lower() for keyword in ['player', 'headshot', 'photo', 'portrait']):
                        print(f"    ✅ Found headshot: {src}")
                        return src
        
        # If no specific player image found, look for any reasonable image
        all_images = soup.find_all('img', src=True)
        for img in all_images:
            src = img.get('src')
            alt = img.get('alt', '').lower()
            
            # Skip obvious non-player images
            if any(skip in src.lower() for skip in ['logo', 'icon', 'flag', 'banner', 'ad', 'sponsor']):
                continue
            if any(skip in alt for skip in ['logo', 'icon', 'flag', 'banner', 'ad', 'sponsor']):
                continue
            
            # Make sure it's a full URL
            if src.startswith('/'):
                src = urljoin('https://www.proballers.com', src)
            
            # Check if it's a reasonable image URL
            if any(ext in src.lower() for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']):
                print(f"    ✅ Found image: {src}")
                return src
        
        print(f"    ❌ No headshot found")
        return "NON_FOUND"
        
    except Exception as e:
        print(f"    ❌ Error: {e}")
        return "NON_FOUND"


def scrape_headshots(players, output_filename="headshots.csv"):
    """Scrape headshots for all players"""
    
    # Check if output file exists
    if os.path.exists(output_filename):
        choice = input(f"{output_filename} exists. (a)ppend, (o)verwrite, or (c)ancel? ").lower().strip()
        if choice == 'c':
            return
        mode = 'w' if choice == 'o' else 'a'
    else:
        mode = 'w'
    
    # Setup driver
    print("🚀 Setting up Chrome WebDriver...")
    driver = setup_chrome_driver()
    
    try:
        with open(output_filename, mode, newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write header if creating new file
            if mode == 'w':
                writer.writerow(['player_name', 'headshot_url'])
            
            successful_extractions = 0
            
            for i, player in enumerate(players, 1):
                print(f"\n[{i}/{len(players)}] Processing: {player['name']}")
                
                try:
                    headshot_url = extract_headshot_url(driver, player['url'])
                    
                    if headshot_url != "NON_FOUND":
                        successful_extractions += 1
                    
                    # Write to CSV
                    writer.writerow([player['name'], headshot_url])
                    csvfile.flush()
                    
                except Exception as e:
                    print(f"    ❌ Error processing {player['name']}: {e}")
                    writer.writerow([player['name'], "NON_FOUND"])
                    csvfile.flush()
                
                # Progress update every 10 players
                if i % 10 == 0:
                    success_rate = (successful_extractions / i) * 100
                    print(f"\n📊 Progress: {i}/{len(players)} ({i/len(players)*100:.1f}%)")
                    print(f"📈 Success rate: {successful_extractions}/{i} ({success_rate:.1f}%)")
                
                # Small delay to be respectful to the server
                time.sleep(0.5)
        
        print(f"\n✅ Completed! Results saved to {output_filename}")
        print(f"📊 Final stats: {successful_extractions}/{len(players)} headshots found ({successful_extractions/len(players)*100:.1f}%)")
        
    except KeyboardInterrupt:
        print("\n⚠️  Interrupted by user")
        print(f"Progress saved to {output_filename}")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        print("🔒 Closing browser...")
        driver.quit()


def main():
    """Main function"""
    print("Israeli Winner League - Headshot Scraper")
    print("=" * 45)
    
    # Load player URLs
    players = load_player_urls()
    
    if not players:
        sys.exit(1)
    
    # Scrape headshots
    scrape_headshots(players)


if __name__ == "__main__":
    main()
