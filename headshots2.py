#!/usr/bin/env python3
"""
Basketball Player Headshot Finder - Selenium Version

This script uses Selenium to automate browser interactions with the Pro Ballers
website, bypassing Cloudflare protection to find player headshot URLs.

Usage: python headshots2.py <input_file>

Requirements:
- pip install selenium
- Chrome browser installed
- ChromeDriver (will be installed automatically with selenium 4.6+)
"""

import sys
import csv
import time
import json
from urllib.parse import quote_plus

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
except ImportError:
    print("Error: Selenium is required for this script.")
    print("Please install it with: pip install selenium")
    sys.exit(1)


def setup_driver():
    """Set up Chrome WebDriver with options to avoid detection."""
    chrome_options = Options()
    
    # Add arguments to make the browser less detectable
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # Set user agent to look like a real browser
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    # Uncomment the next line to run in headless mode (no browser window)
    # chrome_options.add_argument("--headless")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)

        # Set timeouts to prevent hanging
        driver.set_page_load_timeout(60)  # 60 second page load timeout
        driver.implicitly_wait(10)  # 10 second element wait timeout

        # Execute script to remove webdriver property
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        return driver
    except Exception as e:
        print(f"Error setting up Chrome driver: {e}")
        print("Make sure Chrome browser is installed.")
        print("ChromeDriver will be downloaded automatically with Selenium 4.6+")
        sys.exit(1)


def search_player_selenium(driver, player_name):
    """
    Use Google search to find the player's Pro Ballers page directly.

    Args:
        driver: Selenium WebDriver instance
        player_name (str): Name of the player to search for

    Returns:
        str: Headshot URL if found, "NOT_FOUND" otherwise
    """
    try:
        print(f"  Searching for: {player_name}")

        # Try direct Pro Ballers search first
        print("    Trying direct Pro Ballers search...")

        # Navigate to Pro Ballers and try their search
        try:
            driver.get("https://www.proballers.com")
            time.sleep(3)

            # Look for search input
            search_input = None
            search_selectors = [
                "input[type='search']",
                "input[placeholder*='search']",
                "input[placeholder*='Search']",
                "input[name*='search']",
                "input[id*='search']"
            ]

            for selector in search_selectors:
                try:
                    search_input = driver.find_element(By.CSS_SELECTOR, selector)
                    if search_input.is_displayed():
                        break
                except NoSuchElementException:
                    continue

            if search_input:
                print("    Found Pro Ballers search box, trying search...")
                search_input.clear()
                search_input.send_keys(player_name)

                # Wait for suggestions
                time.sleep(2)

                # Try to submit search
                from selenium.webdriver.common.keys import Keys
                search_input.send_keys(Keys.RETURN)
                time.sleep(3)

                # Look for player links in results
                player_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='/basketball/player/']")

                if player_links:
                    print(f"    Found {len(player_links)} player links from Pro Ballers search")

                    for i, link in enumerate(player_links[:3]):
                        try:
                            player_url = link.get_attribute('href')
                            link_text = link.text.strip()

                            print(f"      Checking Pro Ballers result {i+1}: {link_text}")

                            if player_url and "/basketball/player/" in player_url:
                                driver.get(player_url)
                                time.sleep(3)

                                if "/basketball/player/" in driver.current_url:
                                    headshot_url = get_headshot_from_player_page(driver, driver.current_url, player_name)
                                    if headshot_url != "NOT_FOUND":
                                        return headshot_url
                        except Exception as e:
                            print(f"        Error with result {i+1}: {str(e)}")
                            continue

        except Exception as e:
            print(f"    Pro Ballers search failed: {str(e)}")

        # Fallback to Google search
        print("    Falling back to Google search...")
        search_query = f'site:proballers.com "{player_name}" basketball player'
        google_url = f"https://www.google.com/search?q={quote_plus(search_query)}"

        print(f"    Using Google search: {search_query}")

        try:
            driver.get(google_url)
            time.sleep(5)

            # Look for Pro Ballers player links in Google search results
            print("    Looking for Pro Ballers player links in Google results...")

            # Find Pro Ballers player links in Google search results
            # Look specifically in the search results area, not navigation links
            google_result_selectors = [
                "#search a[href*='proballers.com/basketball/player/']",
                ".g a[href*='proballers.com/basketball/player/']",
                "[data-ved] a[href*='proballers.com/basketball/player/']",
                "h3 a[href*='proballers.com/basketball/player/']",
                "a[href*='proballers.com/basketball/player/']"
            ]

            player_links = []
            for selector in google_result_selectors:
                try:
                    links = driver.find_elements(By.CSS_SELECTOR, selector)
                    # Filter out duplicate links and non-player links
                    for link in links:
                        href = link.get_attribute('href')
                        if (href and "/basketball/player/" in href and
                            href not in [existing.get_attribute('href') for existing in player_links]):
                            # Skip Google's internal links
                            if not any(skip in href for skip in ['google.com', 'accounts.google', 'support.google']):
                                player_links.append(link)
                except NoSuchElementException:
                    continue

            if not player_links:
                print("    No Pro Ballers player links found in Google results")
                return "NOT_FOUND"

            print(f"    Found {len(player_links)} Pro Ballers player links")

            # Try each player link
            for i, link in enumerate(player_links[:3]):  # Try first 3 results
                try:
                    player_url = link.get_attribute('href')
                    link_text = link.text.strip()

                    print(f"      Checking Google result {i+1}: {link_text}")
                    print(f"        Raw URL: {player_url}")

                    if player_url and "/basketball/player/" in player_url:
                        print(f"        Valid player URL found")

                        # Navigate directly to the player page
                        try:
                            print(f"        Navigating to player page...")
                            driver.get(player_url)

                            # Wait for page to load
                            try:
                                WebDriverWait(driver, 15).until(
                                    lambda d: d.execute_script("return document.readyState") == "complete"
                                )
                            except TimeoutException:
                                print("        Page load timeout, continuing...")

                            # Verify we're on a player page
                            current_url = driver.current_url
                            if "/basketball/player/" in current_url:
                                print(f"        Successfully navigated to: {current_url}")

                                # Get headshot from the player page
                                headshot_url = get_headshot_from_player_page(driver, current_url, player_name)
                                if headshot_url != "NOT_FOUND":
                                    return headshot_url
                            else:
                                print(f"        Navigation failed, current URL: {current_url}")

                        except TimeoutException:
                            print(f"        Navigation timeout")
                            continue
                        except Exception as nav_error:
                            print(f"        Navigation error: {str(nav_error)}")
                            continue
                    else:
                        print(f"        Not a player URL, skipping")

                except Exception as e:
                    print(f"        Error with Google result {i+1}: {str(e)}")
                    continue

        except Exception as e:
            print(f"    Google search failed: {str(e)}")

        return "NOT_FOUND"

    except Exception as e:
        print(f"  Error in search: {str(e)}")
        return "NOT_FOUND"


def get_headshot_from_player_page(driver, player_url, player_name):
    """
    Extract headshot URL from a player's page.

    Args:
        driver: Selenium WebDriver instance
        player_url (str): URL of the player's page
        player_name (str): Name of the player for verification

    Returns:
        str: Headshot URL if found, "NOT_FOUND" otherwise
    """
    try:
        print(f"        Looking for headshot on player page...")

        # Wait for page to load completely
        try:
            WebDriverWait(driver, 10).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
        except TimeoutException:
            print("        Page load timeout, continuing anyway...")

        time.sleep(2)  # Additional wait for dynamic content

        # Look for the main player image with multiple strategies
        img_selectors = [
            # Most specific selectors first
            "img[src*='proballers.com'][src*='resize_600']",
            "img[src*='proballers.com'][src*='ul/player']",
            "img[src*='proballers.com'][src*='player']",
            "img[src*='proballers.com'][src*='media/cache']",
            # Common player photo containers
            ".player-photo img",
            ".player-image img",
            ".player-avatar img",
            ".profile-image img",
            # Alt text based
            f"img[alt*='{player_name.split()[0]}']",
            # Generic image selectors
            "img[src*='proballers.com']"
        ]

        found_images = []

        for selector in img_selectors:
            try:
                images = driver.find_elements(By.CSS_SELECTOR, selector)

                for img in images:
                    try:
                        src = img.get_attribute('src')
                        alt = img.get_attribute('alt') or ''

                        if src and 'proballers.com' in src:
                            # Skip obvious non-headshot images
                            skip_keywords = ['logo', 'flag', 'country', 'team', 'sponsor', 'banner']
                            if any(keyword in src.lower() for keyword in skip_keywords):
                                continue

                            # Score the image based on how likely it is to be a headshot
                            score = 0

                            # High priority indicators
                            if 'resize_600' in src.lower():
                                score += 10
                            if 'ul/player' in src.lower():
                                score += 8
                            if 'player' in src.lower():
                                score += 5
                            if 'media/cache' in src.lower():
                                score += 3

                            # Alt text matching
                            if alt:
                                name_parts = player_name.lower().split()
                                for part in name_parts:
                                    if part in alt.lower():
                                        score += 5

                            found_images.append((src, score, alt))

                    except Exception as img_error:
                        continue

            except Exception as selector_error:
                continue

        # Sort images by score (highest first) and return the best match
        if found_images:
            found_images.sort(key=lambda x: x[1], reverse=True)
            best_image = found_images[0]

            print(f"        Found headshot (score {best_image[1]}): {best_image[0]}")
            if best_image[2]:
                print(f"        Alt text: {best_image[2]}")

            return best_image[0]

        print("        No headshot found on player page")
        return "NOT_FOUND"

    except Exception as e:
        print(f"        Error extracting headshot: {str(e)}")
        return "NOT_FOUND"


def process_players_file(input_filename):
    """
    Process the input file and search for player headshots using Selenium.
    
    Args:
        input_filename (str): Path to the input text file containing player names
    """
    output_filename = input_filename.replace('.txt', '_headshots_selenium.csv')
    
    try:
        with open(input_filename, 'r', encoding='utf-8') as infile:
            player_names = [line.strip() for line in infile if line.strip()]
        
        print(f"Found {len(player_names)} players to process")
        
        # Set up Selenium driver
        print("Setting up Chrome browser...")
        driver = setup_driver()
        
        try:
            results = []

            for i, player_name in enumerate(player_names, 1):
                print(f"\nProcessing {i}/{len(player_names)}: {player_name}")
                print("=" * 50)

                try:
                    headshot_url = search_player_selenium(driver, player_name)
                    results.append([player_name, headshot_url])

                    print(f"  Result: {headshot_url}")

                    # Add delay between players to be respectful
                    if i < len(player_names):
                        print("  Waiting 5 seconds before next player...")
                        time.sleep(5)

                except Exception as player_error:
                    print(f"  Error processing {player_name}: {str(player_error)}")
                    results.append([player_name, "ERROR"])

                    # Try to recover by going back to homepage
                    try:
                        print("  Attempting to recover...")
                        driver.get("https://www.proballers.com")
                        time.sleep(3)
                    except Exception:
                        pass
            
            # Write results to CSV
            with open(output_filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['Player Name', 'Headshot URL'])
                writer.writerows(results)
            
            print(f"\nResults saved to: {output_filename}")
            
            # Print summary
            found_count = sum(1 for result in results if result[1] != "NOT_FOUND")
            print(f"Summary: {found_count}/{len(results)} players found")
            
        finally:
            # Always close the browser
            driver.quit()
            print("Browser closed.")
        
    except FileNotFoundError:
        print(f"Error: File '{input_filename}' not found")
        sys.exit(1)
    except Exception as e:
        print(f"Error processing file: {str(e)}")
        sys.exit(1)


def main():
    """Main function to handle command line arguments and run the program."""
    if len(sys.argv) != 2:
        print("Usage: python headshots2.py <input_file>")
        print("Example: python headshots2.py player_names_germany.txt")
        sys.exit(1)
    
    input_file = sys.argv[1]
    
    print("Basketball Player Headshot Finder - Selenium Version")
    print("=" * 55)
    print(f"Input file: {input_file}")
    
    process_players_file(input_file)


if __name__ == "__main__":
    main()
