#!/usr/bin/env python3
"""
Script to scrape player headshots from the Israeli Winner League
Navigates to each player's individual page and extracts headshot URLs
Saves results to headshots.csv
"""

import time
import sys
import csv
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse


def setup_chrome_driver():
    """
    Set up Chrome WebDriver with options to mimic a real browser
    """
    chrome_options = Options()
    
    # Add arguments to make the browser look more like a real user
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # Set user agent to look like a real Chrome browser
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    # Optional: Run in headless mode (uncomment if you don't want to see the browser)
    # chrome_options.add_argument("--headless")
    
    # Set up the service with ChromeDriverManager
    service = Service(ChromeDriverManager().install())
    
    # Create the driver
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    # Execute script to remove webdriver property
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver


def get_player_links(driver):
    """
    Get all player links from the main page
    """
    url = "https://www.proballers.com/basketball/league/172/israel-winner-league/players"
    
    print(f"Navigating to: {url}")
    driver.get(url)
    
    # Wait for the page to load
    print("Waiting for page to load...")
    wait = WebDriverWait(driver, 20)
    
    try:
        # Wait for player links to be present
        wait.until(EC.presence_of_element_located((By.XPATH, "//a[contains(@href, '/basketball/player/')]")))
        print("Found player links using XPath")
    except TimeoutException:
        print("Timeout waiting for player links")
        return []
    
    # Give extra time for dynamic content to load
    time.sleep(3)
    
    # Get the page source and parse with BeautifulSoup
    page_source = driver.page_source
    soup = BeautifulSoup(page_source, 'html.parser')
    
    # Find all player links
    player_links = soup.find_all('a', href=lambda x: x and '/basketball/player/' in x)
    
    print(f"Found {len(player_links)} player links")
    
    # Extract player data with URLs
    players = []
    processed_names = set()
    
    for link in player_links:
        player_name = link.get_text(strip=True)
        
        # Skip empty or invalid names
        if not player_name or len(player_name) <= 1 or player_name in processed_names:
            continue
        
        # Clean up the player name
        player_name = ' '.join(player_name.split())
        processed_names.add(player_name)
        
        # Get the player's page URL
        player_url = link.get('href')
        if player_url:
            # Make sure it's a full URL
            if player_url.startswith('/'):
                player_url = urljoin('https://www.proballers.com', player_url)
            
            players.append({
                'name': player_name,
                'url': player_url
            })
    
    return players


def extract_headshot_url(driver, player_url):
    """
    Navigate to a player's page and extract their headshot URL
    """
    try:
        print(f"  Navigating to player page: {player_url}")

        # Set page load timeout
        driver.set_page_load_timeout(15)

        try:
            driver.get(player_url)
        except Exception as e:
            print(f"  Page load timeout or error: {e}")
            return "NON_FOUND"

        # Wait for page to load with shorter timeout
        wait = WebDriverWait(driver, 8)
        try:
            wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        except TimeoutException:
            print(f"  Timeout waiting for page body")
            return "NON_FOUND"

        # Give a shorter moment for images to load
        time.sleep(1)
        
        # Get page source and parse
        page_source = driver.page_source
        soup = BeautifulSoup(page_source, 'html.parser')
        
        # Look for player headshot image
        # Try different common selectors for player photos
        headshot_selectors = [
            'img[alt*="player"]',
            'img[alt*="Player"]',
            'img[src*="player"]',
            'img[src*="headshot"]',
            'img[src*="photo"]',
            '.player-photo img',
            '.player-image img',
            '.headshot img',
            'img[class*="player"]',
            'img[class*="photo"]',
            'img[class*="headshot"]'
        ]
        
        headshot_url = None
        
        for selector in headshot_selectors:
            img_elements = soup.select(selector)
            for img in img_elements:
                src = img.get('src')
                if src:
                    # Make sure it's a full URL
                    if src.startswith('/'):
                        src = urljoin('https://www.proballers.com', src)
                    
                    # Check if this looks like a player photo (not a logo or icon)
                    if any(keyword in src.lower() for keyword in ['player', 'headshot', 'photo', 'portrait']):
                        headshot_url = src
                        break
            
            if headshot_url:
                break
        
        # If no specific player photo found, look for any reasonable sized image
        if not headshot_url:
            all_images = soup.find_all('img', src=True)
            for img in all_images:
                src = img.get('src')
                alt = img.get('alt', '').lower()
                
                # Skip obvious non-player images
                if any(skip in src.lower() for skip in ['logo', 'icon', 'flag', 'banner', 'ad']):
                    continue
                if any(skip in alt for skip in ['logo', 'icon', 'flag', 'banner', 'ad']):
                    continue
                
                # Make sure it's a full URL
                if src.startswith('/'):
                    src = urljoin('https://www.proballers.com', src)
                
                # Check if it's a reasonable image URL
                if any(ext in src.lower() for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']):
                    headshot_url = src
                    break
        
        if headshot_url:
            print(f"  Found headshot: {headshot_url}")
            return headshot_url
        else:
            print(f"  No headshot found")
            return "NON_FOUND"
            
    except Exception as e:
        print(f"  Error extracting headshot: {e}")
        return "NON_FOUND"


def main():
    """Main function"""
    print("Israeli Winner League Player Headshots Scraper")
    print("=" * 55)
    
    # Set up the driver
    print("Setting up Chrome WebDriver...")
    driver = setup_chrome_driver()
    
    # CSV file setup
    csv_filename = "headshots.csv"
    
    # Check if CSV exists and ask user what to do
    if os.path.exists(csv_filename):
        choice = input(f"{csv_filename} already exists. (a)ppend, (o)verwrite, or (c)ancel? ").lower().strip()
        if choice == 'c':
            print("Operation cancelled.")
            driver.quit()
            return
        elif choice == 'o':
            mode = 'w'
            print(f"Overwriting {csv_filename}")
        else:
            mode = 'a'
            print(f"Appending to {csv_filename}")
    else:
        mode = 'w'
        print(f"Creating new {csv_filename}")
    
    try:
        # Get all player links
        players = get_player_links(driver)
        
        if not players:
            print("No players found. Exiting.")
            return
        
        print(f"\nFound {len(players)} players. Starting headshot extraction...")
        
        # Open CSV file for writing
        with open(csv_filename, mode, newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write header if creating new file
            if mode == 'w':
                writer.writerow(['player_name', 'headshot_url'])
            
            # Process each player
            for i, player in enumerate(players, 1):
                print(f"\n[{i}/{len(players)}] Processing: {player['name']}")

                try:
                    # Extract headshot URL
                    headshot_url = extract_headshot_url(driver, player['url'])

                    # Write to CSV
                    writer.writerow([player['name'], headshot_url])

                    # Flush the file to ensure data is written
                    csvfile.flush()

                    print(f"  ✅ Saved: {headshot_url}")

                except Exception as e:
                    print(f"  ❌ Error processing {player['name']}: {e}")
                    # Still write to CSV with NON_FOUND
                    writer.writerow([player['name'], "NON_FOUND"])
                    csvfile.flush()

                # Small delay to be respectful to the server
                time.sleep(0.5)

                # Progress update every 10 players
                if i % 10 == 0:
                    print(f"\n📊 Progress: {i}/{len(players)} players processed ({i/len(players)*100:.1f}%)")

                # Optional: Allow user to interrupt gracefully
                if i % 50 == 0:
                    print("Press Ctrl+C to stop gracefully...")
                    time.sleep(0.5)
        
        print(f"\n✅ Completed! Results saved to {csv_filename}")
        print(f"Processed {len(players)} players")
        
    except KeyboardInterrupt:
        print("\n⚠️  Operation interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during processing: {e}")
    finally:
        print("Closing browser...")
        driver.quit()


if __name__ == "__main__":
    main()
