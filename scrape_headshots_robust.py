#!/usr/bin/env python3
"""
Robust script to scrape player headshots from the Israeli Winner League
Uses multiple strategies and better error handling
"""

import time
import sys
import csv
import os
import requests
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
from urllib.parse import urljoin


def setup_chrome_driver(headless=False):
    """Set up Chrome WebDriver with robust options"""
    chrome_options = Options()
    
    # Basic options
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-images")  # Don't load images on main page for speed
    chrome_options.add_argument("--disable-javascript")  # Disable JS for faster loading
    
    # Anti-detection
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # User agent
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    if headless:
        chrome_options.add_argument("--headless")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    # Set timeouts
    driver.set_page_load_timeout(30)
    driver.implicitly_wait(5)
    
    # Remove webdriver property
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver


def get_player_links_from_saved_data():
    """
    Get player links from our previously saved data instead of scraping again
    This is more reliable and faster
    """
    players = []
    
    # Read from our previous successful scrape
    filename = "israel_winner_league_players_selenium_20250805_190105.txt"
    
    if not os.path.exists(filename):
        print(f"Previous data file {filename} not found. Using fallback method.")
        return get_player_links_fallback()
    
    print(f"Reading player data from {filename}")
    
    with open(filename, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    current_player = None
    for line in lines:
        line = line.strip()
        if line and line[0].isdigit() and '.' in line:
            # This is a player name line like "1. AdamRaz"
            parts = line.split('.', 1)
            if len(parts) == 2:
                player_name = parts[1].strip()
                # Convert back to proper format (add space between first and last name)
                # This is a simple heuristic - look for capital letter in middle
                formatted_name = ""
                for i, char in enumerate(player_name):
                    if i > 0 and char.isupper() and player_name[i-1].islower():
                        formatted_name += " " + char
                    else:
                        formatted_name += char
                
                # Create player URL based on the pattern we observed
                # We'll need to construct this differently since we don't have the exact URLs
                current_player = formatted_name
                players.append(current_player)
    
    print(f"Found {len(players)} players from saved data")
    return players


def get_player_links_fallback():
    """Fallback method with hardcoded player names"""
    players = [
        "Adam Raz", "Jalen Adams", "Brendan Adams", "Amit Aharoni", "Ariel Aizik",
        "Eidan Alber", "Josh Aldrich", "Rawle Alkins", "Bryon Allen", "Shizz Alston",
        "Justin Alston", "Ben Altshuler", "Ben Amar", "Shahar Amir", "Yariv Amiram"
        # Add more players as needed...
    ]
    return players


def try_requests_method(player_name):
    """
    Try to get player headshot using requests instead of Selenium
    This might work better for some cases
    """
    try:
        # Convert player name to URL format
        name_parts = player_name.lower().split()
        if len(name_parts) >= 2:
            # Try common URL patterns
            url_patterns = [
                f"https://www.proballers.com/basketball/player/{name_parts[1]}-{name_parts[0]}",
                f"https://www.proballers.com/basketball/player/{name_parts[0]}-{name_parts[1]}",
            ]
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
            
            for url in url_patterns:
                try:
                    response = requests.get(url, headers=headers, timeout=10)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        # Look for images
                        images = soup.find_all('img', src=True)
                        for img in images:
                            src = img.get('src')
                            if src and any(keyword in src.lower() for keyword in ['player', 'headshot', 'photo']):
                                if src.startswith('/'):
                                    src = urljoin('https://www.proballers.com', src)
                                return src
                        
                        # If no specific player image, return the first reasonable image
                        for img in images:
                            src = img.get('src')
                            if src and not any(skip in src.lower() for skip in ['logo', 'icon', 'flag', 'banner']):
                                if src.startswith('/'):
                                    src = urljoin('https://www.proballers.com', src)
                                return src
                                
                except requests.RequestException:
                    continue
                    
    except Exception as e:
        print(f"    Requests method failed: {e}")
    
    return "NON_FOUND"


def extract_headshot_selenium(driver, player_name):
    """
    Try to extract headshot using Selenium with very short timeouts
    """
    try:
        # Convert name to URL - this is a guess based on the pattern we saw
        name_parts = player_name.lower().split()
        if len(name_parts) < 2:
            return "NON_FOUND"
        
        # Try different URL patterns
        url_patterns = [
            f"https://www.proballers.com/basketball/player/{name_parts[1]}-{name_parts[0]}",
            f"https://www.proballers.com/basketball/player/{name_parts[0]}-{name_parts[1]}",
        ]
        
        for url in url_patterns:
            try:
                print(f"    Trying: {url}")
                driver.get(url)
                
                # Very short wait
                time.sleep(2)
                
                # Look for images
                images = driver.find_elements(By.TAG_NAME, "img")
                for img in images:
                    src = img.get_attribute('src')
                    if src and any(keyword in src.lower() for keyword in ['player', 'headshot', 'photo']):
                        return src
                
                # If found any images, return the first one that's not a logo
                for img in images:
                    src = img.get_attribute('src')
                    if src and not any(skip in src.lower() for skip in ['logo', 'icon', 'flag', 'banner']):
                        return src
                        
            except Exception as e:
                print(f"    Selenium attempt failed: {e}")
                continue
                
    except Exception as e:
        print(f"    Selenium method failed: {e}")
    
    return "NON_FOUND"


def main():
    """Main function"""
    print("Israeli Winner League Player Headshots Scraper (Robust)")
    print("=" * 60)
    
    # Get player names
    players = get_player_links_from_saved_data()
    
    if not players:
        print("No players found. Exiting.")
        return
    
    # CSV setup
    csv_filename = "headshots.csv"
    
    if os.path.exists(csv_filename):
        choice = input(f"{csv_filename} exists. (a)ppend, (o)verwrite, or (c)ancel? ").lower().strip()
        if choice == 'c':
            return
        mode = 'w' if choice == 'o' else 'a'
    else:
        mode = 'w'
    
    # Setup driver (headless for speed)
    print("Setting up Chrome WebDriver (headless mode)...")
    driver = None
    try:
        driver = setup_chrome_driver(headless=True)
    except Exception as e:
        print(f"Failed to setup Selenium driver: {e}")
        print("Will use requests-only method")
    
    try:
        with open(csv_filename, mode, newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            if mode == 'w':
                writer.writerow(['player_name', 'headshot_url'])
            
            for i, player_name in enumerate(players, 1):
                print(f"\n[{i}/{len(players)}] Processing: {player_name}")
                
                headshot_url = "NON_FOUND"
                
                # Try requests method first (faster)
                print("  Trying requests method...")
                headshot_url = try_requests_method(player_name)
                
                # If requests failed and we have Selenium, try that
                if headshot_url == "NON_FOUND" and driver:
                    print("  Trying Selenium method...")
                    headshot_url = extract_headshot_selenium(driver, player_name)
                
                # Write result
                writer.writerow([player_name, headshot_url])
                csvfile.flush()
                
                print(f"  Result: {headshot_url}")
                
                # Progress update
                if i % 10 == 0:
                    print(f"\n📊 Progress: {i}/{len(players)} ({i/len(players)*100:.1f}%)")
                
                # Small delay
                time.sleep(0.2)
        
        print(f"\n✅ Completed! Results saved to {csv_filename}")
        
    except KeyboardInterrupt:
        print("\n⚠️  Interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        if driver:
            driver.quit()


if __name__ == "__main__":
    main()
