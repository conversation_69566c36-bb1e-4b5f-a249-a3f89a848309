# Basketball Player Headshot Finder

This Python utility helps you find headshot images of basketball players from the Pro Ballers website. The script attempts to use the Pro Ballers search API but falls back to manual lookup due to Cloudflare anti-bot protection.

## Features

- Reads player names from a text file
- Attempts automated API search (currently blocked by Cloudflare)
- Generates optimized search URLs for Pro Ballers website
- Creates both CSV and HTML helper files for efficient manual lookup
- Handles different name formats (First Last, Last First)
- Provides interactive HTML interface for streamlined workflow

## Requirements

- Python 3.6 or higher
- No additional packages required

## Usage

1. Create a text file with player names (one per line):
   ```
   <PERSON><PERSON><PERSON>richs <PERSON>
   ```

2. Run the script:
   ```bash
   python3 headshots.py player_names_germany.txt
   ```

3. The script will create a CSV file with search URLs and instructions.

## Output

The script creates two files:
1. **CSV file** with five columns:
   - **Player Name**: The original player name from the input file
   - **Search URL**: Optimized search URL for Pro Ballers website
   - **Player URL**: (Empty, to be filled manually)
   - **Headshot URL**: (Empty, to be filled manually)
   - **Status**: PENDING (to be updated based on results)

2. **HTML helper file**: Interactive web interface for efficient manual lookup

## Manual Lookup Process

### Option 1: HTML Helper with API Integration (Recommended)
1. Open the generated HTML file in your web browser
2. Click "Search Player" button for each player
3. **If API works**: Select the correct player from the search results - the tool will automatically fetch the player page and headshot URL
4. **If API is blocked**: Use the fallback manual search link that appears, then paste URLs into the provided fields
5. Click "Download Updated CSV" to get the completed results

**Note**: The HTML helper attempts to use the Pro Ballers `search_player` API directly. If successful, it will automatically populate player and headshot URLs. If the API is blocked by Cloudflare, it falls back to manual search.

### Option 2: Direct CSV Editing
1. Open the generated CSV file in a spreadsheet application
2. For each player, visit the Search URL
3. Find the player's profile page in the search results
4. Copy the player page URL to the "Player URL" column
5. Right-click on the main headshot image and copy the image URL
6. Paste the image URL in the "Headshot URL" column
7. Update the "Status" column (FOUND, NOT_FOUND, NO_HEADSHOT)
8. Save the updated CSV file

## Image URL Patterns

Look for headshot images with URLs containing:
- `proballers.com/media/cache/resize_600_png/`
- `proballers.com/ul/player/`

These patterns typically indicate the main player headshot images.

## Example

Input file `player_names_germany.txt`:
```
Herkenhoff Philipp
Hermannsson Martin
```

Output file `player_names_germany_headshots.csv`:
```csv
Player Name,Search URL,Instructions
Herkenhoff Philipp,https://www.proballers.com/search?q=Philipp+Herkenhoff,"Visit the search URL, find the player's page, right-click on headshot image, and copy the image URL"
Hermannsson Martin,https://www.proballers.com/search?q=Martin+Hermannsson,"Visit the search URL, find the player's page, right-click on headshot image, and copy the image URL"
```

After manual lookup, your final CSV might look like:
```csv
Player Name,Headshot URL
Herkenhoff Philipp,https://www.proballers.com/media/cache/resize_600_png/https---www.proballers.com/ul/player/philipp-herkenhoff2-1ef7cdf3-493b-6346-9d19-75fbcbb486b4.png
Hermannsson Martin,NOT_FOUND
```

See `example_completed_headshots.csv` for a complete example.

## Tips

- The script automatically tries different name formats (Last First, First Last) to improve search results
- If a player is not found with one name format, try manually searching with alternative spellings
- Some players may have multiple profiles; choose the most recent or relevant one
- If no headshot is available, you can use "NOT_FOUND" as the URL value

## Troubleshooting

- **File not found**: Make sure the input file exists and the path is correct
- **No search results**: Try alternative spellings or check if the player is in the Pro Ballers database
- **Empty results**: Verify that the input file contains player names (one per line)

## Why Manual Lookup?

The Pro Ballers website uses anti-bot protection that blocks automated scraping attempts. Manual lookup ensures:
- Respect for the website's terms of service
- Accurate results by human verification
- Ability to handle edge cases and alternative spellings
- No risk of IP blocking or rate limiting

## License

This script is provided as-is for educational and personal use. Please respect the Pro Ballers website's terms of service when using their search functionality.
