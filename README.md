# Israeli Winner League Player Headshots Scraper

This repository contains scripts to download player URLs and scrape headshot images from the Israeli Winner League basketball players on ProBallers.com.

## Files

### Main Scripts

1. **`get_player_urls.py`** - ✅ **Step 1: Get Player URLs**
   - Uses Selenium WebDriver to scrape the main players page
   - Extracts all player names and their individual page URLs
   - Saves results to `player_urls.csv`
   - <PERSON>les 246+ players from the 2024-2025 season

2. **`scrape_headshots.py`** - ✅ **Step 2: Extract Headshots**
   - Reads player URLs from `player_urls.csv`
   - Navigates to each player's individual page
   - Extracts headshot image URLs using multiple detection strategies
   - Saves results to `headshots.csv` with format: `<player_name>,<headshot_url>`
   - Uses "NON_FOUND" when no headshot is available

### Dependencies

- **`requirements.txt`** - Contains all required Python packages:
  - `requests>=2.25.1`
  - `beautifulsoup4>=4.9.3`
  - `lxml>=4.6.3`
  - `selenium>=4.0.0`
  - `webdriver-manager>=3.8.0`

### Output Files

- **`player_urls.csv`** - Generated by Step 1: Contains player names and URLs
- **`headshots.csv`** - Generated by Step 2: Contains player names and headshot URLs

## Usage

### Step 1: Get Player URLs

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the URL extractor:
   ```bash
   python3 get_player_urls.py
   ```

3. This will:
   - Open Chrome browser (headless mode)
   - Navigate to the ProBallers Israeli Winner League page
   - Extract all player names and URLs
   - Save results to `player_urls.csv`

### Step 2: Extract Headshots

1. Run the headshot scraper:
   ```bash
   python3 scrape_headshots.py
   ```

2. This will:
   - Read player URLs from `player_urls.csv`
   - Visit each player's individual page
   - Extract headshot image URLs
   - Save results to `headshots.csv`

## Features

- **Two-Step Process**: Separate URL extraction and headshot scraping for better reliability
- **246+ Players**: All players from the 2024-2025 Israeli Winner League season
- **Robust Error Handling**: Continues processing even when individual pages fail
- **Progress Tracking**: Shows current progress and success rates
- **CSV Output**: Clean CSV format for easy data processing
- **Headless Mode**: Runs faster without showing browser windows
- **Multiple Detection Strategies**: Uses various methods to find player headshots

## Sample Output

### player_urls.csv
```csv
player_name,player_url
Adam Raz,https://www.proballers.com/basketball/player/74417/raz-adam
Adams Jalen,https://www.proballers.com/basketball/player/69235/jalen-adams
```

### headshots.csv
```csv
player_name,headshot_url
Adam Raz,https://www.proballers.com/media/cache/resize_600_png/https---www.proballers.com/ul/player/59711-1-61d375cb618d2.jpg
Adams Jalen,NON_FOUND
Adams Brendan,https://www.proballers.com/media/cache/resize_600_png/https---www.proballers.com/ul/player/adams-brenda-1efbffcf-8a84-62a0-b8f6-0de5927cf6e1.png
```

## Technical Notes

- **Modular Design**: Two separate scripts for better maintainability and debugging
- **ChromeDriverManager**: Automatic Chrome driver management
- **Anti-Detection**: Browser automation includes measures to avoid detection
- **BeautifulSoup**: Used for robust HTML parsing
- **Error Recovery**: Graceful handling of timeouts and page load failures
- **Progress Persistence**: CSV files are updated in real-time

## Workflow

1. **Step 1** extracts player URLs once and saves them to CSV
2. **Step 2** can be run multiple times, resuming from where it left off
3. If Step 2 fails partway through, you can resume by choosing "append" mode
4. Both scripts provide detailed progress information and error reporting

## Source

Data scraped from: https://www.proballers.com/basketball/league/172/israel-winner-league/players

## Requirements

- Python 3.7+
- Chrome browser (for Selenium)
- Internet connection