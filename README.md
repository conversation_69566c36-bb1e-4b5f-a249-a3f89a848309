# Israeli Winner League Players Scraper

This repository contains scripts to download and display the list of all players from the Israeli Winner League basketball from ProBallers.com.

## Files

### Main Scripts

1. **`scrape_israel_players_selenium.py`** - ✅ **WORKING**
   - Uses Selenium WebDriver to impersonate Chrome browser
   - Successfully bypasses website restrictions
   - Extracts all 246 players with their details (team, age, height, country)
   - Saves results to timestamped text file

2. **`scrape_israel_players.py`** - ❌ **NOT WORKING**
   - Uses requests library with headers to mimic browser
   - Gets blocked with 403 Forbidden error
   - Kept for reference

3. **`parse_israel_players.py`** - ✅ **WORKING**
   - Uses pre-extracted data from the website
   - Fallback solution with hardcoded player list
   - Good for when website is completely inaccessible

### Dependencies

- **`requirements.txt`** - Contains all required Python packages:
  - `requests>=2.25.1`
  - `beautifulsoup4>=4.9.3`
  - `lxml>=4.6.3`
  - `selenium>=4.0.0`
  - `webdriver-manager>=3.8.0`

### Output Files

- **`israel_winner_league_players_selenium_[timestamp].txt`** - Generated output file with all players
- **`debug_page_source.html`** - Debug file containing the scraped HTML (for troubleshooting)

## Usage

### Recommended Method (Selenium)

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the Selenium scraper:
   ```bash
   python3 scrape_israel_players_selenium.py
   ```

3. The script will:
   - Open a Chrome browser window
   - Navigate to the ProBallers website
   - Extract all player information
   - Display results in terminal
   - Optionally save to a timestamped text file

### Alternative Method (Pre-extracted Data)

If the website is inaccessible, use the fallback script:
```bash
python3 parse_israel_players.py
```

## Features

- **Complete Player Data**: Name, team, age, height, country
- **246 Players**: All players from the 2024-2025 Israeli Winner League season
- **Multiple Teams**: Some players are listed with multiple teams
- **International Players**: Players from various countries including Israel, USA, France, Canada, etc.
- **Automatic Sorting**: Players are sorted alphabetically by name
- **File Export**: Option to save results to text file

## Sample Output

```
  1. AdamRaz
     Team: Ironi Kiryat Ata
     Age: 25
     Height: 1m92
     Country: Israel

  2. AdamsBrendan
     Team: Hapoel Holon
     Age: 25
     Height: 1m92
     Country: United States
```

## Technical Notes

- The Selenium script uses ChromeDriverManager for automatic driver management
- Browser automation includes anti-detection measures
- BeautifulSoup is used for HTML parsing
- The script handles dynamic content loading with appropriate waits
- Debug output is saved for troubleshooting

## Source

Data scraped from: https://www.proballers.com/basketball/league/172/israel-winner-league/players

## Requirements

- Python 3.7+
- Chrome browser (for Selenium)
- Internet connection