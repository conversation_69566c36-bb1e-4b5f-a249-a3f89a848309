#!/usr/bin/env python3
"""
Test script to debug single player page loading
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup


def setup_chrome_driver():
    """Set up Chrome WebDriver"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver


def test_player_page():
    """Test loading a single player page"""
    driver = setup_chrome_driver()
    
    try:
        # Test URL - Adam Raz
        test_url = "https://www.proballers.com/basketball/player/74417/raz-adam"
        
        print(f"Testing player page: {test_url}")
        
        # Set timeouts
        driver.set_page_load_timeout(20)
        driver.implicitly_wait(10)
        
        print("Loading page...")
        start_time = time.time()
        
        try:
            driver.get(test_url)
            load_time = time.time() - start_time
            print(f"Page loaded successfully in {load_time:.2f} seconds")
        except Exception as e:
            print(f"Error loading page: {e}")
            return
        
        # Wait for body
        print("Waiting for page body...")
        wait = WebDriverWait(driver, 10)
        try:
            wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            print("Body found")
        except TimeoutException:
            print("Timeout waiting for body")
            return
        
        # Get page title
        title = driver.title
        print(f"Page title: {title}")
        
        # Check for images
        print("Looking for images...")
        images = driver.find_elements(By.TAG_NAME, "img")
        print(f"Found {len(images)} images")
        
        # Get page source and analyze
        page_source = driver.page_source
        soup = BeautifulSoup(page_source, 'html.parser')
        
        # Look for player images
        print("\nAnalyzing images:")
        for i, img in enumerate(images[:10]):  # Check first 10 images
            src = img.get_attribute('src')
            alt = img.get_attribute('alt') or ''
            print(f"  Image {i+1}: {src} (alt: {alt})")
        
        # Save page source for inspection
        with open('test_player_page.html', 'w', encoding='utf-8') as f:
            f.write(page_source)
        print("\nPage source saved to test_player_page.html")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        print("Closing browser...")
        driver.quit()


if __name__ == "__main__":
    test_player_page()
