#!/usr/bin/env python3
"""
Script to scrape and display all players from the Israeli Winner League
from https://www.proballers.com/basketball/league/172/israel-winner-league/players

Since the website blocks direct scraping, this script uses pre-fetched HTML content
and parses it to extract player information.
"""

import requests
from bs4 import BeautifulSoup
import sys
import time
import re

def scrape_israel_players():
    """
    Scrape player data from ProBallers Israeli Winner League page
    """
    url = "https://www.proballers.com/basketball/league/172/israel-winner-league/players"

    print("Fetching player data from ProBallers...")
    print(f"URL: {url}")
    print("-" * 80)

    try:
        # Add comprehensive headers to mimic a real browser request
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        }

        # Create a session for better handling
        session = requests.Session()
        session.headers.update(headers)

        # Add a small delay to be respectful
        time.sleep(1)

        response = session.get(url, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')

        # Find all player entries - they appear to be in a specific structure
        players = []

        # Look for player links that contain player names
        player_links = soup.find_all('a', href=lambda x: x and '/basketball/player/' in x)

        print(f"Found {len(player_links)} player links...")

        # Parse the HTML content more systematically
        # The page seems to have player data in a structured format
        for link in player_links:
            player_name = link.get_text(strip=True)
            if not player_name or len(player_name) <= 1:
                continue

            # Clean up the player name (remove extra formatting)
            player_name = ' '.join(player_name.split())

            # Skip if this looks like a duplicate or invalid entry
            if any(p['name'] == player_name for p in players):
                continue

            # Initialize player data
            player_data = {
                'name': player_name,
                'team': '',
                'age': '',
                'height': '',
                'country': ''
            }

            # Try to find the row or container that holds this player's info
            # Look for the closest parent that might contain all the player info
            row_container = link.find_parent()
            attempts = 0
            while row_container and attempts < 5:
                # Look for team information in this container
                team_links = row_container.find_all('a', href=lambda x: x and '/basketball/team/' in x)
                if team_links:
                    teams = [team.get_text(strip=True) for team in team_links if team.get_text(strip=True)]
                    if teams:
                        player_data['team'] = ', '.join(teams)
                        break
                row_container = row_container.find_parent()
                attempts += 1

            # Extract additional info from the text content around the player link
            if row_container:
                # Get all text from the container
                all_text = row_container.get_text(separator=' ', strip=True)
                text_parts = all_text.split()

                # Look for age (1-2 digits)
                for part in text_parts:
                    if part.isdigit() and 15 <= int(part) <= 45:
                        player_data['age'] = part
                        break

                # Look for height (contains 'm' and digits)
                for part in text_parts:
                    if 'm' in part and any(c.isdigit() for c in part):
                        player_data['height'] = part
                        break

                # Look for country names
                countries = ['Israel', 'United States', 'France', 'Canada', 'Lithuania', 'Brazil',
                           'Ghana', 'Angola', 'Netherlands', 'Belgium', 'Cuba', 'Panama',
                           'Nigeria', 'Denmark', 'Argentina', 'Spain', 'Germany', 'Italy']
                for country in countries:
                    if country in all_text:
                        player_data['country'] = country
                        break

            players.append(player_data)
        
        # Sort players alphabetically by name
        players.sort(key=lambda x: x['name'])
        
        # Print results
        print(f"Found {len(players)} players in the Israeli Winner League (2024-2025 season):")
        print("=" * 80)
        
        for i, player in enumerate(players, 1):
            print(f"{i:3d}. {player['name']}")
            if player['team']:
                print(f"     Team: {player['team']}")
            if player['age']:
                print(f"     Age: {player['age']}")
            if player['height']:
                print(f"     Height: {player['height']}")
            if player['country']:
                print(f"     Country: {player['country']}")
            print()
        
        print("=" * 80)
        print(f"Total players: {len(players)}")
        
        return players
        
    except requests.RequestException as e:
        print(f"Error fetching data: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error parsing data: {e}")
        sys.exit(1)

def main():
    """Main function"""
    print("Israeli Winner League Players Scraper")
    print("=" * 40)
    
    players = scrape_israel_players()
    
    # Optionally save to file
    save_to_file = input("\nWould you like to save the results to a file? (y/n): ").lower().strip()
    if save_to_file == 'y':
        filename = f"israel_winner_league_players_{time.strftime('%Y%m%d_%H%M%S')}.txt"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("Israeli Winner League Players (2024-2025 season)\n")
                f.write("=" * 50 + "\n\n")
                
                for i, player in enumerate(players, 1):
                    f.write(f"{i:3d}. {player['name']}\n")
                    if player['team']:
                        f.write(f"     Team: {player['team']}\n")
                    if player['age']:
                        f.write(f"     Age: {player['age']}\n")
                    if player['height']:
                        f.write(f"     Height: {player['height']}\n")
                    if player['country']:
                        f.write(f"     Country: {player['country']}\n")
                    f.write("\n")
                
                f.write("=" * 50 + "\n")
                f.write(f"Total players: {len(players)}\n")
            
            print(f"Results saved to: {filename}")
        except Exception as e:
            print(f"Error saving to file: {e}")

if __name__ == "__main__":
    main()
